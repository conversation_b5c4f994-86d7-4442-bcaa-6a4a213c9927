<template>
  <!-- Modal Overlay with enhanced backdrop -->
  <div
    v-if="show"
    class="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4 transition-opacity duration-300 ease-in-out"
    @click="$emit('close')"
    role="dialog"
    aria-modal="true"
    aria-labelledby="certificate-title"
    aria-describedby="certificate-description"
  >
    <!-- Modal Container with enhanced styling -->
    <div
      class="bg-white rounded-xl shadow-2xl border border-gray-200 p-4 sm:p-6 lg:p-8 max-w-4xl w-full max-h-[90vh] overflow-y-auto transition-all duration-300 ease-in-out transform scale-100"
      @click.stop
    >
      <!-- Header with improved styling -->
      <div class="flex justify-between items-center mb-6 pb-4 border-b border-gray-100">
        <div>
          <h3
            id="certificate-title"
            class="text-xl sm:text-2xl font-bold text-gray-900 mb-1"
          >
            E-Certificate
          </h3>
          <p
            id="certificate-description"
            class="text-sm text-gray-600 font-medium"
          >
            {{ classTitle }}
          </p>
        </div>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 transition-all duration-200 p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          aria-label="Close certificate view"
          tabindex="0"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Certificate Image Container with enhanced styling -->
      <div class="mb-8">
        <div class="relative bg-gray-50 rounded-xl border border-gray-200 overflow-hidden">
          <!-- Certificate Image -->
          <img
            v-show="!imageLoading && !imageError"
            :src="finalCertificateImageUrl"
            :alt="`Certificate for ${classTitle}`"
            class="w-full h-auto transition-opacity duration-300 ease-in-out"
            @error="handleImageError"
            @load="handleImageLoad"
          />

          <!-- Enhanced Loading state -->
          <div
            v-if="imageLoading"
            class="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 min-h-[400px]"
          >
            <div class="text-center">
              <div class="relative">
                <div class="animate-spin rounded-full h-12 w-12 border-4 border-orange-200 border-t-orange-600 mx-auto mb-4"></div>
                <div class="absolute inset-0 rounded-full border-4 border-transparent border-t-orange-300 animate-pulse"></div>
              </div>
              <p class="text-base font-medium text-gray-700 mb-1">Loading certificate...</p>
              <p class="text-sm text-gray-500">Please wait while we prepare your certificate</p>
            </div>
          </div>

          <!-- Enhanced Error state -->
          <div
            v-if="imageError"
            class="flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 min-h-[400px] border-2 border-dashed border-gray-300 rounded-xl"
          >
            <div class="text-center p-8 max-w-md">
              <div class="w-20 h-20 mx-auto mb-6 bg-gray-200 rounded-full flex items-center justify-center">
                <svg class="h-10 w-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h4 class="text-lg font-semibold text-gray-800 mb-2">Certificate Not Available</h4>
              <p class="text-sm text-gray-600 mb-3">We're unable to load your certificate image at this time.</p>
              <p class="text-xs text-gray-500 bg-gray-100 px-3 py-2 rounded-lg inline-block">
                Please contact our support team for assistance
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Enhanced Download Button Section -->
      <div class="flex flex-col sm:flex-row justify-center items-center gap-4">
        <button
          @click="downloadCertificate"
          :disabled="!isDownloadAvailable"
          class="group relative px-8 py-3 bg-orange-600 text-white rounded-lg text-sm font-semibold hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200 transform disabled:bg-gray-400 disabled:cursor-not-allowed disabled:transform-none disabled:hover:scale-100 flex items-center min-w-[200px] justify-center"
          :class="{ 'opacity-50': !isDownloadAvailable }"
          aria-label="Download certificate"
        >
          <!-- Download Icon (default state) -->
          <svg
            v-if="!downloadInProgress"
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-2 transition-transform duration-200 group-hover:translate-y-0.5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>

          <!-- Loading Spinner (download in progress) -->
          <div
            v-if="downloadInProgress"
            class="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2"
            aria-hidden="true"
          ></div>

          <span>
            {{
              imageError ? 'Download Unavailable' :
              imageLoading ? 'Preparing...' :
              downloadInProgress ? 'Downloading...' :
              'Download Certificate'
            }}
          </span>
        </button>

        <!-- Additional info for completed certificate -->
        <div v-if="isDownloadAvailable" class="text-center sm:text-left">
          <p class="text-xs text-gray-500 font-medium">
            Completed on {{ formattedDate }}
          </p>
          <p class="text-xs text-gray-400">
            High-quality PDF format
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CertificateView',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    classId: {
      type: [String, Number],
      required: true
    },
    classTitle: {
      type: String,
      required: true
    },
    studentName: {
      type: String,
      default: 'Student Name'
    },
    completionDate: {
      type: String,
      default: null
    },
    certificateImageUrl: {
      type: String,
      default: null
    }
  },
  emits: ['close', 'download'],
  data() {
    return {
      imageLoading: true,
      imageError: false,
      downloadInProgress: false
    };
  },
  computed: {
    formattedDate() {
      if (this.completionDate) {
        return this.completionDate;
      }

      // Default to current date if no completion date provided
      const now = new Date();
      const day = String(now.getDate()).padStart(2, '0');
      const month = now.toLocaleString('default', { month: 'long' });
      const year = now.getFullYear();

      return `${day} ${month} ${year}`;
    },

    // Generate certificate image URL based on class data
    finalCertificateImageUrl() {
      // If certificateImageUrl prop is provided, use it
      if (this.certificateImageUrl) {
        console.log(`Using provided certificate URL: ${this.certificateImageUrl}`);
        return this.certificateImageUrl;
      }

      // Generate certificate image path based on classId as fallback
      // Admin uploads certificates with naming convention: certificate-{classId}.jpg/png
      // In a real application, this would be handled by the backend
      const fallbackUrl = `/certificates/certificate-${this.classId}.jpg`;
      console.log(`Using fallback certificate URL: ${fallbackUrl}`);
      return fallbackUrl;
    },

    // Check if download is available
    isDownloadAvailable() {
      return !this.imageError && !this.imageLoading && !this.downloadInProgress;
    }
  },
  watch: {
    show(newVal) {
      if (newVal) {
        // Reset states when modal is shown
        this.imageLoading = true;
        this.imageError = false;
        this.downloadInProgress = false;

        // Log certificate information for debugging
        console.log('CertificateView opened:', {
          classId: this.classId,
          classTitle: this.classTitle,
          certificateImageUrl: this.certificateImageUrl,
          finalUrl: this.finalCertificateImageUrl
        });

        // Add keyboard event listener for ESC key
        document.addEventListener('keydown', this.handleKeydown);

        // Prevent body scroll when modal is open
        document.body.style.overflow = 'hidden';
      } else {
        // Clean up when modal is closed
        document.removeEventListener('keydown', this.handleKeydown);
        document.body.style.overflow = '';
      }
    },

    // Watch for changes in certificate URL
    finalCertificateImageUrl(newUrl, oldUrl) {
      if (newUrl !== oldUrl && this.show) {
        console.log(`Certificate URL changed from ${oldUrl} to ${newUrl}`);
        // Reset loading state when URL changes
        this.imageLoading = true;
        this.imageError = false;
      }
    }
  },
  beforeUnmount() {
    // Clean up event listeners and body styles
    document.removeEventListener('keydown', this.handleKeydown);
    document.body.style.overflow = '';
  },
  methods: {
    handleImageLoad() {
      this.imageLoading = false;
      this.imageError = false;
      console.log(`Certificate image loaded successfully: ${this.finalCertificateImageUrl}`);
    },

    handleImageError() {
      this.imageLoading = false;
      this.imageError = true;
      console.warn(`Certificate image failed to load for class ${this.classId}: ${this.finalCertificateImageUrl}`);

      // Additional debugging information
      console.warn('Certificate loading error details:', {
        classId: this.classId,
        classTitle: this.classTitle,
        providedUrl: this.certificateImageUrl,
        finalUrl: this.finalCertificateImageUrl,
        timestamp: new Date().toISOString()
      });
    },

    handleKeydown(event) {
      // Close modal on ESC key press
      if (event.key === 'Escape') {
        this.closeModal();
      }
    },

    closeModal() {
      this.$emit('close');
    },

    async downloadCertificate() {
      // Validate prerequisites
      if (!this.classId) {
        console.error('Cannot download certificate: Missing class ID');
        return;
      }

      if (this.imageError) {
        console.error('Cannot download certificate: Image not available');
        return;
      }

      if (this.downloadInProgress) {
        return; // Prevent multiple simultaneous downloads
      }

      try {
        this.downloadInProgress = true;

        console.log(`Downloading certificate for class ${this.classId}: ${this.classTitle}`);

        // Simulate download process with a small delay for better UX
        await new Promise(resolve => setTimeout(resolve, 1000));

        // In a real application, this would trigger the actual download
        // For now, we'll just emit an event to show the success dialog
        this.$emit('download', {
          classId: this.classId,
          classTitle: this.classTitle,
          studentName: this.studentName,
          completionDate: this.formattedDate,
          certificateUrl: this.finalCertificateImageUrl
        });

      } catch (error) {
        console.error('Download failed:', error);
        // In a real app, you might want to show an error message to the user
      } finally {
        this.downloadInProgress = false;
      }
    }
  }
}
</script>
